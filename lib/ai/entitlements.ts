import type { UserType } from '@/lib/db/schema';
import { chatModelIds, type ChatModel } from './models';

interface Entitlements {
  maxMessagesPerDay: number;
  availableChatModelIds: Array<ChatModel['id']>;
}

// For backwards compatibility, extend the Record type to include 'regular'
type UserTypeWithBackwardsCompat = UserType | 'regular';

export const entitlementsByUserType: Record<UserTypeWithBackwardsCompat, Entitlements> = {
  /*
   * For regular users with an account
   */
  user: {
    maxMessagesPerDay: 500,
    availableChatModelIds: chatModelIds,
  },

  /*
   * For admin users
   */
  admin: {
    maxMessagesPerDay: 1000,
    availableChatModelIds: chatModelIds,
  },

  /*
   * Legacy support for 'regular' type (same as 'user')
   */
  regular: {
    maxMessagesPerDay: 500,
    availableChatModelIds: chatModelIds,
  },
};
