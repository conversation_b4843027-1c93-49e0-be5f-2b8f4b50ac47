import * as dotenv from 'dotenv';
import { Client } from '@notionhq/client';

dotenv.config();

const notion = new Client({ auth: process.env.NOTION_TOKEN });

type Comment = {
  author: string;
  message: string;
  comment: string;
  url: string;
}

export async function addCommentToNotion(record: Comment) {
  const MAX_COMMENT_LENGTH = 40;
  const comment = record.comment.length > MAX_COMMENT_LENGTH ? record.comment.substring(0, MAX_COMMENT_LENGTH) + '...' : record.comment;
  try {
    const response = await notion.pages.create({
      parent: {
        database_id: process.env.NOTION_COMMENTS_DATABASE_ID as string,
      },
      properties: {
        Title: { 
          title: [
            {
              text: {
                content: comment,
              },
            },
          ],
        },
        Author: {  
          rich_text: [
            {
              text: {
                content: record.author,
              },
            },
          ]
        },
        Message: {
          rich_text: [
            {
              text: {
                content: record.message.trim(),
              },
            },
          ],
        },
        'Full Comment': {
          rich_text: [
            {
              text: {
                content: record.comment.trim(),
              },
            },
          ],
        },
        'Comment Date': {
          date: {
            start: new Date().toISOString(),
          },
        },
        'Conversation URL': {
          url: record.url,
        },
      },
    });

    console.log('Row added successfully:', response.id);
    return response;
  } catch (error) {
    console.error('Error adding row to Notion:', error);
    return null;
  }
}