'use client';

import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useActionState, useEffect, useState } from 'react';
import { useTranslations } from 'next-intl';

import { RegisterForm } from '@/components/register-form';
import { SubmitButton } from '@/components/submit-button';

import { register, type RegisterActionState } from '../actions';
import { toast } from '@/components/toast';
import { useSession } from 'next-auth/react';

export default function Page() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const t = useTranslations('auth');

  const [email, setEmail] = useState('');
  const [isSuccessful, setIsSuccessful] = useState(false);
  const [inviteToken, setInviteToken] = useState(searchParams.get('invite') || '');
  // Get redirect URL and validate it's safe (internal path only)
  const rawRedirectUrl = searchParams.get('redirect');
  const redirectUrl = rawRedirectUrl?.startsWith('/') && !rawRedirectUrl.startsWith('//')
    ? rawRedirectUrl
    : '/';

  const { update: updateSession } = useSession();

  const [state, formAction] = useActionState<RegisterActionState, FormData>(
    register,
    {
      status: 'idle',
    },
  );



  useEffect(() => {
    // Safety check to prevent undefined state errors
    if (!state || !state.status) return;

    if (state.status === 'user_exists') {
      toast({ type: 'error', description: t('accountExists') });
    } else if (state.status === 'failed') {
      toast({ type: 'error', description: t('createAccountFailed') });
    } else if (state.status === 'invalid_data') {
      toast({
        type: 'error',
        description: t('validationFailed'),
      });
    } else if (state.status === 'invalid_invite') {
      toast({
        type: 'error',
        description: 'Invalid or expired invite code',
      });
    } else if (state.status === 'invite_used') {
      toast({
        type: 'error',
        description: 'This invite has already been used',
      });
    } else if (state.status === 'invite_expired') {
      toast({
        type: 'error',
        description: 'This invite has expired',
      });
    } else if (state.status === 'success') {
      toast({ type: 'success', description: t('accountCreated') });

      setIsSuccessful(true);
      // Update session and then redirect
      updateSession().then(() => {
        window.location.href = redirectUrl;
      });
    }
  }, [state, t, router, redirectUrl, updateSession]);

  const handleSubmit = (formData: FormData) => {
    setEmail(formData.get('email') as string);
    setInviteToken(formData.get('inviteToken') as string);
    formAction(formData);
  };

  return (
    <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl gap-12 flex flex-col">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
          <h3 className="text-xl font-semibold dark:text-zinc-50">{t('signUp')}</h3>
          <p className="text-sm text-gray-500 dark:text-zinc-400">
            {t('signUpDescription')}
          </p>
        </div>
        <RegisterForm
          action={handleSubmit}
          defaultEmail={email}
          defaultInviteToken={inviteToken}
        >
          <SubmitButton isSuccessful={isSuccessful}>{t('signUp')}</SubmitButton>
          <p className="text-center text-sm text-gray-600 mt-4 dark:text-zinc-400">
            {t('haveAccount')}{' '}
            <Link
              href="/login"
              className="font-semibold text-gray-800 hover:underline dark:text-zinc-200"
            >
              {t('signIn')}
            </Link>
            {' '}{t('instead')}
          </p>
        </RegisterForm>
      </div>
    </div>
  );
}
