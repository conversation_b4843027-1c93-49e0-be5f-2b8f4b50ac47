'use client';

import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useActionState, useEffect, useState } from 'react';
import { toast } from '@/components/toast';
import { useTranslations } from 'next-intl';

import { AuthForm } from '@/components/auth-form';
import { SubmitButton } from '@/components/submit-button';

import { login, type LoginActionState } from '../actions';
import { useSession } from 'next-auth/react';

export default function Page() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const t = useTranslations('auth');

  const [email, setEmail] = useState('');
  const [isSuccessful, setIsSuccessful] = useState(false);
  // Get redirect URL and validate it's safe (internal path only)
  const rawRedirectUrl = searchParams.get('redirect');
  const redirectUrl = rawRedirectUrl?.startsWith('/') && !rawRedirectUrl.startsWith('//')
    ? rawRedirectUrl
    : '/';

  const { update: updateSession } = useSession();

  const [state, formAction] = useActionState<LoginActionState, FormData>(
    login,
    {
      status: 'idle',
    },
  );



  useEffect(() => {
    // Safety check to prevent undefined state errors
    if (!state || !state.status) return;

    if (state.status === 'failed') {
      toast({
        type: 'error',
        description: t('invalidCredentials'),
      });
    } else if (state.status === 'invalid_data') {
      toast({
        type: 'error',
        description: t('validationFailed'),
      });
    } else if (state.status === 'success') {
      setIsSuccessful(true);
      console.log('redirectUrl', redirectUrl);
      // Update session and then redirect
      updateSession().then(() => {
        window.location.href = redirectUrl;
      });
    }
  }, [state, t, router, redirectUrl, updateSession]);

  const handleSubmit = (formData: FormData) => {
    setEmail(formData.get('email') as string);
    formAction(formData);
  };

  return (
    <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-12">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
          <h3 className="text-xl font-semibold dark:text-zinc-50">{t('signIn')}</h3>
          <p className="text-sm text-gray-500 dark:text-zinc-400">
            {t('signInDescription')}
          </p>
        </div>
        <AuthForm action={handleSubmit} defaultEmail={email}>
          <SubmitButton isSuccessful={isSuccessful}>{t('signIn')}</SubmitButton>
          <div className="text-center text-sm text-gray-600 mt-4 space-y-2 dark:text-zinc-400">
            <p>
              <Link
                href="/reset-password"
                className="font-semibold text-gray-800 hover:underline dark:text-zinc-200"
              >
                {t('forgotPassword')}
              </Link>
            </p>
          </div>
        </AuthForm>
      </div>
    </div>
  );
}
