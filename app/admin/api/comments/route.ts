import { auth } from '@/app/(auth)/auth';
import { getAllComments } from '@/lib/db/queries';
import { NextResponse } from 'next/server';

export async function GET() {
  const session = await auth();

  if (!session || !session.user || session.user.type !== 'admin') {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const comments = await getAllComments();
    return NextResponse.json(comments);
  } catch (error) {
    console.error('Failed to fetch comments:', error);
    return new Response('Internal server error', { status: 500 });
  }
}