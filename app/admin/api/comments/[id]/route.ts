import { auth } from '@/app/(auth)/auth';
import { deleteComment } from '@/lib/db/queries';

export async function DELETE(_request: Request, { params }: { params: { id: string } }) {
  const session = await auth();

  if (!session || !session.user || session.user.type !== 'admin') {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const [deletedComment] = await deleteComment({
      id: params.id,
      userId: session.user.id,
      isAdmin: true,
    });

    if (!deletedComment) {
      return new Response('Comment not found', { status: 404 });
    }

    return new Response('Comment deleted', { status: 200 });
  } catch (error) {
    console.error('Failed to delete comment:', error);
    return new Response('Internal server error', { status: 500 });
  }
}