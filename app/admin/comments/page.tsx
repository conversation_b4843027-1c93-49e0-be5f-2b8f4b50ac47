'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { toast } from '@/components/toast';
import type { Comment } from '@/lib/db/schema';

interface CommentWithUser extends Comment {
  userEmail?: string;
  userType?: string;
}

export default function CommentsPage() {
  const [comments, setComments] = useState<CommentWithUser[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchComments = async () => {
    try {
      const response = await fetch('/admin/api/comments');
      if (response.ok) {
        const data = await response.json();
        setComments(data);
      } else {
        toast({ type: 'error', description: 'Failed to fetch comments' });
      }
    } catch (error) {
      console.error('Failed to fetch comments:', error);
      toast({ type: 'error', description: 'Failed to fetch comments' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchComments();
  }, []);

  const handleDeleteComment = async (commentId: string) => {
    if (!confirm('Are you sure you want to delete this comment?')) {
      return;
    }

    try {
      const response = await fetch(`/admin/api/comments/${encodeURIComponent(commentId)}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast({ type: 'success', description: 'Comment deleted successfully' });
        fetchComments(); // Refresh the list
      } else {
        const errorText = await response.text();
        toast({ type: 'error', description: errorText || 'Failed to delete comment' });
      }
    } catch (error) {
      console.error('Failed to delete comment:', error);
      toast({ type: 'error', description: 'Failed to delete comment' });
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getUserTypeColor = (type: string) => {
    switch (type) {
      case 'admin':
        return 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-950';
      case 'user':
        return 'text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-950';
      default:
        return 'text-gray-600 bg-gray-50 dark:text-gray-400 dark:bg-gray-950';
    }
  };

  const formatUserType = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  return (
    <div className="flex flex-col h-screen overflow-y-auto">
      <header className="flex sticky z-50 top-0 bg-background py-2 items-center px-4 gap-2">
        <div className="flex items-center justify-between w-full">
          <div>
            <h2 className="text-xl font-semibold">Comments Management</h2>
          </div>
        </div>
      </header>
      
      <div className="flex-1 p-4">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full size-8 border-b-2 border-primary" />
          </div>
        ) : (
          <div className="rounded-md border">
            <table className="w-full">
              <thead>
                <tr className="border-b bg-muted/50">
                  <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                    Comment
                  </th>
                  <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                    User
                  </th>
                  <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                    Type
                  </th>
                  <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                    Message ID
                  </th>
                  <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                    Dates
                  </th>
                  <th className="h-12 px-4 text-right align-middle font-medium text-muted-foreground">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {comments.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="h-24 text-center text-muted-foreground">
                      No comments found.
                    </td>
                  </tr>
                ) : (
                  comments.map((comment) => (
                    <tr key={comment.id} className="border-b transition-colors hover:bg-muted/50">
                      <td className="p-4 align-middle max-w-md">
                        <div className="font-medium line-clamp-3">{comment.content}</div>
                      </td>
                      <td className="p-4 align-middle">
                        <div className="font-medium">{comment.userEmail || 'Unknown User'}</div>
                      </td>
                      <td className="p-4 align-middle">
                        {comment.userType && (
                          <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getUserTypeColor(comment.userType)}`}>
                            {formatUserType(comment.userType)}
                          </span>
                        )}
                      </td>
                      <td className="p-4 align-middle">
                        <code className="text-xs text-muted-foreground font-mono bg-muted px-1 rounded">
                          {comment.messageId}
                        </code>
                      </td>
                      <td className="p-4 align-middle">
                        <div className="text-sm text-muted-foreground space-y-1">
                          <div>Created: {formatDate(comment.createdAt)}</div>
                          <div>Updated: {formatDate(comment.updatedAt)}</div>
                        </div>
                      </td>
                      <td className="p-4 align-middle text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteComment(comment.id)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="size-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}