'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { InviteUserModal } from '@/components/invite-user-modal';
import { Badge } from '@/components/ui/badge';
import { Trash2, Copy, Check } from 'lucide-react';
import { toast } from '@/components/toast';
import type { Invite } from '@/lib/db/schema';

interface InviteWithCreator extends Invite {
  createdByEmail?: string;
}

export default function InvitesPage() {
  const [invites, setInvites] = useState<InviteWithCreator[]>([]);
  const [loading, setLoading] = useState(true);
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [copiedToken, setCopiedToken] = useState<string | null>(null);

  const fetchInvites = async () => {
    try {
      const response = await fetch('/admin/api/invites');
      if (response.ok) {
        const data = await response.json();
        setInvites(data.invites || []);
      } else {
        toast({ type: 'error', description: 'Failed to fetch invites' });
      }
    } catch (error) {
      console.error('Failed to fetch invites:', error);
      toast({ type: 'error', description: 'Failed to fetch invites' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInvites();
  }, []);

  const handleRevokeInvite = async (inviteId: string) => {
    if (!confirm('Are you sure you want to revoke this invite?')) {
      return;
    }

    try {
      const response = await fetch(`/admin/api/invites/${inviteId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast({ type: 'success', description: 'Invite revoked successfully' });
        fetchInvites(); // Refresh the list
      } else {
        const error = await response.json();
        toast({ type: 'error', description: error.error || 'Failed to revoke invite' });
      }
    } catch (error) {
      console.error('Failed to revoke invite:', error);
      toast({ type: 'error', description: 'Failed to revoke invite' });
    }
  };

  const handleCopyInviteLink = async (token: string) => {
    const baseUrl = window.location.origin;
    const inviteUrl = `${baseUrl}/register?invite=${encodeURIComponent(token)}`;
    
    try {
      await navigator.clipboard.writeText(inviteUrl);
      setCopiedToken(token);
      toast({ type: 'success', description: 'Invite link copied to clipboard!' });
      
      // Reset copied state after 2 seconds
      setTimeout(() => setCopiedToken(null), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      toast({ type: 'error', description: 'Failed to copy to clipboard' });
    }
  };

  const getStatusBadge = (invite: Invite) => {
    if (invite.usedAt) {
      return <Badge variant="secondary">Used</Badge>;
    }
    if (new Date() > invite.expiresAt) {
      return <Badge variant="destructive">Expired</Badge>;
    }
    return <Badge variant="default">Active</Badge>;
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="flex flex-col h-screen overflow-y-auto">
      <header className="flex sticky z-50 top-0 bg-background py-2 items-center px-4 gap-2">
        <div className="flex items-center justify-between w-full">
          <div>
            <h2 className="text-xl font-semibold">Invites Management</h2>
          </div>
          <Button size="sm" onClick={() => setIsInviteModalOpen(true)}>
            Create Invite
          </Button>
        </div>
      </header>
      
      <div className="flex-1 p-4">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full size-8 border-b-2 border-primary" />
          </div>
        ) : (
          <div className="rounded-md border">
            <table className="w-full">
              <thead>
                <tr className="border-b bg-muted/50">
                  <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                    Email
                  </th>
                  <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                    Status
                  </th>
                  <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                    Token
                  </th>
                  <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground">
                    Dates
                  </th>
                  <th className="h-12 px-4 text-right align-middle font-medium text-muted-foreground">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {invites.length === 0 ? (
                  <tr>
                    <td colSpan={5} className="h-24 text-center text-muted-foreground">
                      No invites found. Create your first invite to get started.
                    </td>
                  </tr>
                ) : (
                  invites.map((invite) => (
                    <tr key={invite.id} className="border-b transition-colors hover:bg-muted/50">
                      <td className="p-4 align-middle">
                        <div className="font-medium">{invite.email}</div>
                      </td>
                      <td className="p-4 align-middle">
                        {getStatusBadge(invite)}
                      </td>
                      <td className="p-4 align-middle">
                        <code className="text-xs text-muted-foreground font-mono bg-muted px-1 rounded">
                          {invite.token}
                        </code>
                      </td>
                      <td className="p-4 align-middle">
                        <div className="text-sm text-muted-foreground space-y-1">
                          <div>Created: {formatDate(invite.createdAt)}</div>
                          <div>Expires: {formatDate(invite.expiresAt)}</div>
                          {invite.usedAt && (
                            <div>Used: {formatDate(invite.usedAt)}</div>
                          )}
                        </div>
                      </td>
                      <td className="p-4 align-middle text-right">
                        <div className="flex items-center justify-end gap-2">
                          {!invite.usedAt && new Date() <= invite.expiresAt && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleCopyInviteLink(invite.token)}
                            >
                              {copiedToken === invite.token ? (
                                <Check className="size-4" />
                              ) : (
                                <Copy className="size-4" />
                              )}
                            </Button>
                          )}
                          {!invite.usedAt && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleRevokeInvite(invite.id)}
                              className="text-destructive hover:text-destructive"
                            >
                              <Trash2 className="size-4" />
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>

      <InviteUserModal 
        open={isInviteModalOpen} 
        onOpenChange={setIsInviteModalOpen}
        onInviteCreated={fetchInvites}
      />
    </div>
  );
}
