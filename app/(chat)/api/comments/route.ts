import { auth } from '@/app/(auth)/auth';
import {
  createComment,
  getCommentsByMessageId,
  deleteComment,
  updateComment,
  getMessageById,
  getChatById,
} from '@/lib/db/queries';
import { addCommentToNotion } from '@/lib/notion-integration';
import { UIMessage } from 'ai';
import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const messageId = searchParams.get('messageId');

  if (!messageId) {
    return new Response('messageId is required', { status: 400 });
  }

  const session = await auth();

  if (!session || !session.user) {
    return new Response('Unauthorized', { status: 401 });
  }

  // Check if user has access to the message
  try {
    const [message] = await getMessageById({ id: messageId });
    if (!message) {
      return new Response('Message not found', { status: 404 });
    }

    const chat = await getChatById({ id: message.chatId });
    if (!chat) {
      return new Response('Chat not found', { status: 404 });
    }

    // Check if user has access to the chat
    if (chat.visibility === 'private' && chat.userId !== session.user.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    const comments = await getCommentsByMessageId({ messageId });
    return NextResponse.json(comments);
  } catch (error) {
    console.error('Failed to get comments:', error);
    return new Response('Internal server error', { status: 500 });
  }
}

export async function POST(request: Request) {
  const session = await auth();

  if (!session || !session.user) {
    return new Response('Unauthorized', { status: 401 });
  }

  // All authenticated users can create comments

  try {
    const { messageId, content } = await request.json();

    if (!messageId || !content) {
      return new Response('messageId and content are required', { status: 400 });
    }

    if (typeof content !== 'string' || content.trim().length === 0) {
      return new Response('Content must be a non-empty string', { status: 400 });
    }

    // Check if user has access to the message
    const [message] = await getMessageById({ id: messageId });
    if (!message) {
      return new Response('Message not found', { status: 404 });
    }

    const chat = await getChatById({ id: message.chatId });
    if (!chat) {
      return new Response('Chat not found', { status: 404 });
    }

    // Check if user has access to the chat
    if (chat.visibility === 'private' && chat.userId !== session.user.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    const [newComment] = await createComment({
      messageId,
      userId: session.user.id,
      content: content.trim(),
    });

    await addCommentToNotion({
      author: session.user.email || 'Unknown',
      message: (message.parts as UIMessage['parts']).map((part: any) => part.text).join('\n'),
      comment: content.trim(),
      url: message.chatId
    });

    return NextResponse.json(newComment, { status: 201 });
  } catch (error) {
    console.error('Failed to create comment:', error);
    return new Response('Internal server error', { status: 500 });
  }
}

export async function PUT(request: Request) {
  const session = await auth();

  if (!session || !session.user) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const { id, content } = await request.json();

    if (!id || !content) {
      return new Response('id and content are required', { status: 400 });
    }

    if (typeof content !== 'string' || content.trim().length === 0) {
      return new Response('Content must be a non-empty string', { status: 400 });
    }

    const [updatedComment] = await updateComment({
      id,
      userId: session.user.id,
      content: content.trim(),
    });

    if (!updatedComment) {
      return new Response('Comment not found or unauthorized', { status: 404 });
    }

    return NextResponse.json(updatedComment);
  } catch (error) {
    console.error('Failed to update comment:', error);
    return new Response('Internal server error', { status: 500 });
  }
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return new Response('id is required', { status: 400 });
  }

  const session = await auth();

  if (!session || !session.user) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const [deletedComment] = await deleteComment({
      id,
      userId: session.user.id,
    });

    if (!deletedComment) {
      return new Response('Comment not found or unauthorized', { status: 404 });
    }

    return new Response('Comment deleted', { status: 200 });
  } catch (error) {
    console.error('Failed to delete comment:', error);
    return new Response('Internal server error', { status: 500 });
  }
}
