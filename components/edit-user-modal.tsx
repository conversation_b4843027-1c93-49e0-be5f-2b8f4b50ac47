'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/toast';
import type { User } from '@/lib/db/schema';

interface EditUserModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: User | null;
  onUserUpdated?: () => void;
}

export function EditUserModal({ open, onOpenChange, user, onUserUpdated }: EditUserModalProps) {
  const [userType, setUserType] = useState<string>('');
  const [password, setPassword] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);

  // Reset form when user changes or modal opens
  React.useEffect(() => {
    if (user && open) {
      setUserType(user.type);
      setPassword('');
    }
  }, [user, open]);

  const handleUpdateUser = async () => {
    if (!user) return;

    setIsUpdating(true);
    try {
      const updates: any = {};
      
      // Only include type if it changed
      if (userType !== user.type) {
        updates.type = userType;
      }
      
      // Only include password if provided
      if (password.trim()) {
        updates.password = password.trim();
      }

      // If no changes, just close the modal
      if (Object.keys(updates).length === 0) {
        onOpenChange(false);
        return;
      }

      const response = await fetch('/admin/api/users', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: user.id,
          ...updates,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update user');
      }

      toast({ type: 'success', description: 'User updated successfully!' });
      onOpenChange(false);
      
      // Call the callback to refresh the users list
      if (onUserUpdated) {
        onUserUpdated();
      }
    } catch (error) {
      console.error('Failed to update user:', error);
      toast({ 
        type: 'error', 
        description: error instanceof Error ? error.message : 'Failed to update user'
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleClose = () => {
    setPassword('');
    onOpenChange(false);
  };

  if (!user) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit User</DialogTitle>
          <DialogDescription>
            Update the user&apos;s account type or set a new password.
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              value={user.email}
              readOnly
              className="bg-muted"
            />
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="userType">Account Type</Label>
            <Select value={userType} onValueChange={setUserType}>
              <SelectTrigger id="userType">
                <SelectValue placeholder="Select account type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="user">User</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="password">New Password</Label>
            <Input
              id="password"
              type="password"
              placeholder="Leave blank for no change"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={isUpdating}
            />
            <p className="text-sm text-muted-foreground">
              Leave blank to keep the current password unchanged.
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isUpdating}>
            Cancel
          </Button>
          <Button onClick={handleUpdateUser} disabled={isUpdating}>
            {isUpdating ? 'Updating...' : 'Update User'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
