'use client';

import { useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { toast } from '@/components/toast';
import type { User } from '@/lib/db/schema';

interface DeleteUserModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: User | null;
  onUserDeleted?: () => void;
}

export function DeleteUserModal({ open, onOpenChange, user, onUserDeleted }: DeleteUserModalProps) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDeleteUser = async () => {
    if (!user) return;

    setIsDeleting(true);
    try {
      const response = await fetch(`/admin/api/users?id=${user.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete user');
      }

      toast({ type: 'success', description: 'User deleted successfully!' });
      onOpenChange(false);
      
      // Call the callback to refresh the users list
      if (onUserDeleted) {
        onUserDeleted();
      }
    } catch (error) {
      console.error('Failed to delete user:', error);
      toast({ 
        type: 'error', 
        description: error instanceof Error ? error.message : 'Failed to delete user'
      });
    } finally {
      setIsDeleting(false);
    }
  };

  if (!user) return null;

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete User</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete the user <strong>{user.email}</strong>?
            <br />
            <br />
            This action cannot be undone. This will permanently delete the user account
            and all associated data including chats, messages, and documents.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDeleteUser}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting ? 'Deleting...' : 'Delete User'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
