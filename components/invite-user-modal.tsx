'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/toast';
import { Copy, Check } from 'lucide-react';

interface InviteUserModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onInviteCreated?: () => void;
}

export function InviteUserModal({ open, onOpenChange, onInviteCreated }: InviteUserModalProps) {
  const [email, setEmail] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [inviteUrl, setInviteUrl] = useState('');
  const [copied, setCopied] = useState(false);

  const handleGenerateInvite = async () => {
    if (!email) {
      toast({ type: 'error', description: 'Please enter an email address' });
      return;
    }

    setIsGenerating(true);
    try {
      const response = await fetch('/admin/api/invites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to generate invite');
      }

      const data = await response.json();
      setInviteUrl(data.invite.inviteUrl);
      toast({ type: 'success', description: 'Invite generated successfully!' });

      // Call the callback to refresh the invites list
      if (onInviteCreated) {
        onInviteCreated();
      }
    } catch (error) {
      console.error('Failed to generate invite:', error);
      toast({ 
        type: 'error', 
        description: error instanceof Error ? error.message : 'Failed to generate invite'
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCopyToClipboard = async () => {
    if (!inviteUrl) return;

    try {
      await navigator.clipboard.writeText(inviteUrl);
      setCopied(true);
      toast({ type: 'success', description: 'Invite link copied to clipboard!' });
      
      // Reset copied state after 2 seconds
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      toast({ type: 'error', description: 'Failed to copy to clipboard' });
    }
  };

  const handleClose = () => {
    setEmail('');
    setInviteUrl('');
    setCopied(false);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Invite User</DialogTitle>
          <DialogDescription>
            Generate an invite link for a new user. The invite will be valid for 7 days.
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={isGenerating}
            />
          </div>
          
          {!inviteUrl ? (
            <Button 
              onClick={handleGenerateInvite} 
              disabled={isGenerating || !email}
              className="w-full"
            >
              {isGenerating ? 'Generating...' : 'Generate Invite'}
            </Button>
          ) : (
            <div className="grid gap-2">
              <Label htmlFor="inviteUrl">Invite Link</Label>
              <div className="flex gap-2">
                <Input
                  id="inviteUrl"
                  value={inviteUrl}
                  readOnly
                  className="flex-1"
                />
                <Button
                  onClick={handleCopyToClipboard}
                  variant="outline"
                  size="icon"
                  className="shrink-0"
                >
                  {copied ? <Check className="size-4" /> : <Copy className="size-4" />}
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Share this link with the user to allow them to register.
              </p>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            {inviteUrl ? 'Done' : 'Cancel'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
