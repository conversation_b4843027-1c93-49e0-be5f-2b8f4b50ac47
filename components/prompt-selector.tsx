'use client';

import { useState, useEffect, useOptimistic, startTransition, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  CheckCircleFillIcon,
  ChevronDownIcon,
  NotebookIcon,
  PencilEditIcon,
} from '@/components/icons';
import type { Prompt } from '@/lib/db/schema';
import { cn } from '@/lib/utils';
import { useSession } from 'next-auth/react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/components/toast';
import { usePrompts } from '@/hooks/use-prompts';
import { savePromptAsCookie } from '@/app/(chat)/actions';
import { useAppState } from '@/hooks/use-app-state';
import { useSubPromptsByParentId } from '@/hooks/use-sub-prompt';
import {
  parsePromptVariables,
  type PromptVariables,
} from '@/lib/utils/prompt-variables';

export function PromptSelector({
  onSelect,
  className,
  selectedPromptId,
  onManagePrompts,
}: {
  onSelect: (prompt: Prompt) => void;
  className?: string;
  selectedPromptId?: string;
  onManagePrompts?: () => void;
}) {
  const { data: session } = useSession();
  const isAdmin = session?.user?.type === 'admin';
  const { prompts, reload } = usePrompts();
  const { setSelectedPrompt } = useAppState();
  const [optimisticPromptId, setOptimisticPromptId] =
    useOptimistic(selectedPromptId);

  // Find the selected prompt from prompts array based on selectedPromptId
  const selectedPrompt = optimisticPromptId
    ? prompts.find((prompt) => prompt.id === optimisticPromptId)
    : null;

  // Fetch sub-prompts using SWR hook for automatic caching and deduplication
  const { subPrompts } = useSubPromptsByParentId(selectedPrompt?.id);

  // If there's a selected prompt ID but no matching prompt found, select the first available prompt
  useEffect(() => {
    if (optimisticPromptId && !selectedPrompt && prompts.length > 0) {
      const firstPrompt = prompts[0];
      startTransition(() => {
        setOptimisticPromptId(firstPrompt.id);
        savePromptAsCookie(firstPrompt.id);

        // Trigger local storage event to notify of cookie change
        window.localStorage.setItem(
          'prompt-cookie-change',
          Date.now().toString(),
        );
        window.dispatchEvent(new Event('storage'));

        onSelect(firstPrompt);
      });
    }
  }, [optimisticPromptId, selectedPrompt, prompts, onSelect]);

  // Already have session data above
  const { status } = useSession();
  const isLoggedIn = status === 'authenticated';

  const [isManageOpen, setIsManageOpen] = useState(false);
  const [selectedPromptIndex, setSelectedPromptIndex] = useState(0);
  const [editedContent, setEditedContent] = useState<string>('');
  const [selectedSubPromptIndex, setSelectedSubPromptIndex] = useState<
    number | null
  >(null);
  const [editedVariables, setEditedVariables] = useState<PromptVariables>({});
  const [activeTab, setActiveTab] = useState<'content' | 'variables'>(
    'content',
  );

  // Memoize the current prompt ID to prevent unnecessary re-renders
  const currentPromptId = useMemo(() => {
    // Only return ID if we have prompts and a valid index
    if (prompts.length > 0 && selectedPromptIndex >= 0 && selectedPromptIndex < prompts.length) {
      return prompts[selectedPromptIndex]?.id;
    }
    return undefined;
  }, [prompts, selectedPromptIndex]);

  // Fetch sub-prompts for the management dialog using SWR
  const { subPrompts: currentSubPrompts, mutate: mutateCurrentSubPrompts } = useSubPromptsByParentId(
    currentPromptId
  );

  // Keep editedContent and editedVariables in sync with selected prompt/sub-prompt
  useEffect(() => {
    // Only proceed if we have prompts and a valid index
    if (prompts.length === 0 || selectedPromptIndex < 0 || selectedPromptIndex >= prompts.length) {
      return;
    }

    if (
      selectedSubPromptIndex !== null &&
      currentSubPrompts[selectedSubPromptIndex]
    ) {
      const subPrompt = currentSubPrompts[selectedSubPromptIndex];
      setEditedContent(subPrompt.content || '');
      // Sub-prompts don't have variables, so clear them
      setEditedVariables({});
    } else {
      const currentPrompt = prompts[selectedPromptIndex];
      if (currentPrompt) {
        setEditedContent(currentPrompt.content || '');
        // Parse variables from the current prompt
        const variables = currentPrompt.variables
          ? parsePromptVariables(currentPrompt.variables)
          : {};
        setEditedVariables(variables);
      }
    }
  }, [selectedPromptIndex, selectedSubPromptIndex, prompts, currentSubPrompts]);

  // Reset sub-prompt selection when prompt changes
  useEffect(() => {
    setSelectedSubPromptIndex(null);
  }, [selectedPromptIndex]);

  const handleUpdate = async () => {
    if (selectedSubPromptIndex !== null) {
      // Update sub-prompt
      const subPrompt = currentSubPrompts[selectedSubPromptIndex];
      if (!subPrompt) return;
      const res = await fetch('/api/sub-prompts', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: subPrompt.id,
          name: subPrompt.name,
          content: editedContent,
        }),
      });
      if (res.ok) {
        toast({
          type: 'success',
          description: 'Sub-prompt updated successfully!',
        });
        // Revalidate sub-prompts using SWR
        mutateCurrentSubPrompts();
      } else {
        toast({ type: 'error', description: 'Failed to update sub-prompt.' });
      }
    } else {
      // Update main prompt
      const prompt = prompts[selectedPromptIndex];
      if (!prompt) return;

      const res = await fetch('/api/prompts', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: prompt.id,
          name: prompt.name,
          content: editedContent,
          variables: editedVariables,
        }),
      });
      if (res.ok) {
        toast({ type: 'success', description: 'Prompt updated successfully!' });
        const updatedPrompts = await reload();
        // Force re-sync of variables after reload
        const updatedPrompt = updatedPrompts[selectedPromptIndex];
        if (updatedPrompt) {
          const variables = updatedPrompt.variables
            ? parsePromptVariables(updatedPrompt.variables)
            : {};
          setEditedVariables(variables);
        }
      } else {
        toast({ type: 'error', description: 'Failed to update prompt.' });
      }
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        asChild
        className={cn(
          'w-fit data-[state=open]:bg-accent data-[state=open]:text-accent-foreground',
          className,
        )}
      >
        <Button
          data-testid="model-selector"
          variant="outline"
          className="md:px-2 md:h-[34px] w-full md:w-fit"
        >
          <NotebookIcon /> Prompt:&nbsp;
          <span className="max-w-[240px] truncate">
            {selectedPrompt ? selectedPrompt.name : '...'}
          </span>
          <ChevronDownIcon />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="min-w-[300px]">
        {prompts.map((prompt) => (
          <DropdownMenuItem
            key={prompt.id}
            data-active={prompt.id === optimisticPromptId}
            onSelect={() => {
              startTransition(() => {
                setOptimisticPromptId(prompt.id);
                savePromptAsCookie(prompt.id);

                // Update global app state
                setSelectedPrompt(prompt);

                // Trigger local storage event to notify of cookie change
                window.localStorage.setItem(
                  'prompt-cookie-change',
                  Date.now().toString(),
                );
                window.dispatchEvent(new Event('storage'));

                onSelect(prompt);
              });
            }}
            asChild
          >
            <button
              type="button"
              className="gap-2 group/item flex flex-row justify-between items-center w-full cursor-pointer"
            >
              <div className="flex flex-col gap-0.5 items-start grow">
                <div className="font-medium">{prompt.name}</div>
                <div className="text-xs text-left text-muted-foreground truncate w-full max-w-[200px]">
                  {new Date(prompt.createdAt || new Date()).toLocaleString(
                    'pl-PL',
                    {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                      hour: '2-digit',
                      minute: '2-digit',
                    },
                  )}
                  {prompt.id === optimisticPromptId &&
                    subPrompts.length > 0 && (
                      <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                        {subPrompts.length} sub-prompt
                        {subPrompts.length !== 1 ? 's' : ''}
                      </div>
                    )}
                </div>
              </div>
              <div className="text-foreground dark:text-foreground opacity-0 group-data-[active=true]/item:opacity-100">
                <CheckCircleFillIcon />
              </div>
            </button>
          </DropdownMenuItem>
        ))}
        {onManagePrompts && isLoggedIn && (
          <>
            <div className="border-t my-1" />
            <DropdownMenuItem asChild>
              <button
                type="button"
                className="w-full cursor-pointer text-primary hover:underline justify-center px-2 py-1.5 text-sm font-medium"
                onClick={() => setIsManageOpen(true)}
              >
                Manage Prompts
              </button>
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
      {/* Modal for Manage Prompts */}
      <Dialog open={isManageOpen} onOpenChange={setIsManageOpen}>
        <DialogContent className="max-w-3xl w-full h-[70vh] flex flex-col p-0">
          <DialogHeader className="px-4 pt-4 pb-0">
            <DialogTitle>Manage Enai System Prompts</DialogTitle>
          </DialogHeader>
          <div className="flex flex-1 flex-row min-h-0 border-y">
            {/* Left: Prompt Titles List */}
            <div className="w-1/3 h-full border-r flex flex-col">
              <div className="flex-1 overflow-y-auto">
                {prompts.map((prompt, idx) => (
                  <div key={prompt.id}>
                    {/* Main Prompt */}
                    <div
                      className={`flex items-center border-b hover:bg-accent focus:bg-accent outline-none justify-between ${
                        selectedPromptIndex === idx &&
                        selectedSubPromptIndex === null
                          ? 'bg-accent/50'
                          : ''
                      }`}
                    >
                      <button
                        className="flex items-center justify-between flex-1 w-0 text-left px-4 py-3 bg-transparent border-none outline-none"
                        onClick={() => {
                          setSelectedPromptIndex(idx);
                          setSelectedSubPromptIndex(null);
                        }}
                        type="button"
                      >
                        {prompt.name}
                        <button
                          type="button"
                          className="p-2 focus:outline-none"
                          title="Edit title"
                          onClick={async (e) => {
                            e.stopPropagation();
                            const newTitle = window.prompt(
                              'Edit prompt title:',
                              prompt.name,
                            );
                            if (!newTitle || newTitle === prompt.name) return;
                            const res = await fetch('/api/prompts', {
                              method: 'PATCH',
                              headers: { 'Content-Type': 'application/json' },
                              body: JSON.stringify({
                                id: prompt.id,
                                name: newTitle,
                                content: prompt.content,
                                variables: parsePromptVariables(
                                  prompt.variables,
                                ),
                              }),
                            });
                            if (res.ok) {
                              toast({
                                type: 'success',
                                description: 'Prompt title updated!',
                              });
                              await reload();
                            } else {
                              toast({
                                type: 'error',
                                description: 'Failed to update prompt title.',
                              });
                            }
                          }}
                        >
                          <PencilEditIcon size={16} />
                        </button>
                      </button>
                    </div>

                    {/* Sub-prompts (shown only for selected prompt) */}
                    {selectedPromptIndex === idx &&
                      currentSubPrompts.map((subPrompt, subIdx) => (
                        <div
                          key={subPrompt.id}
                          className={`flex items-center border-b hover:bg-accent focus:bg-accent outline-none justify-between pl-8 ${
                            selectedSubPromptIndex === subIdx
                              ? 'bg-accent/50'
                              : ''
                          }`}
                        >
                          <button
                            className="flex items-center justify-between flex-1 w-0 text-left px-4 py-2 bg-transparent border-none outline-none text-sm"
                            onClick={() => setSelectedSubPromptIndex(subIdx)}
                            type="button"
                          >
                            ↳ {subPrompt.name}
                            <button
                              type="button"
                              className="p-2 focus:outline-none"
                              title="Edit sub-prompt title"
                              onClick={async (e) => {
                                e.stopPropagation();
                                const newTitle = window.prompt(
                                  'Edit sub-prompt title:',
                                  subPrompt.name,
                                );
                                if (!newTitle || newTitle === subPrompt.name)
                                  return;
                                const res = await fetch('/api/sub-prompts', {
                                  method: 'PATCH',
                                  headers: {
                                    'Content-Type': 'application/json',
                                  },
                                  body: JSON.stringify({
                                    id: subPrompt.id,
                                    name: newTitle,
                                    content: subPrompt.content,
                                  }),
                                });
                                if (res.ok) {
                                  toast({
                                    type: 'success',
                                    description: 'Sub-prompt title updated!',
                                  });
                                  // Revalidate sub-prompts using SWR
                                  mutateCurrentSubPrompts();
                                } else {
                                  toast({
                                    type: 'error',
                                    description:
                                      'Failed to update sub-prompt title.',
                                  });
                                }
                              }}
                            >
                              <PencilEditIcon size={14} />
                            </button>
                          </button>
                        </div>
                      ))}
                  </div>
                ))}
              </div>
              <div className="flex flex-row items-center gap-2 p-2 border-t">
                <button
                  className="flex-1 py-2 text-primary font-medium hover:bg-muted rounded text-sm"
                  type="button"
                  onClick={async () => {
                    const name = window.prompt(
                      'Enter a title for the new prompt:',
                    );
                    if (!name) return;
                    const res = await fetch('/api/prompts', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({
                        name,
                        content: 'You are an AI therapy chatbot.',
                      }),
                    });
                    if (res.ok) {
                      toast({
                        type: 'success',
                        description: 'Prompt created successfully!',
                      });

                      // Reload prompts and immediately use the returned data
                      const updatedPrompts = await reload();

                      // The newly created prompt should always be first since getPrompts sorts by createdAt desc
                      if (updatedPrompts.length > 0) {
                        // Set index to 0 to select the first (newest) prompt
                        setSelectedPromptIndex(0);
                        setSelectedSubPromptIndex(null);
                        setEditedContent(updatedPrompts[0].content);
                      }
                    } else {
                      toast({
                        type: 'error',
                        description: 'Failed to create prompt.',
                      });
                    }
                  }}
                >
                  + New Prompt
                </button>
                <button
                  className="flex-1 py-2 text-primary font-medium hover:bg-muted rounded text-sm"
                  type="button"
                  onClick={async () => {
                    const currentPrompt = prompts[selectedPromptIndex];
                    if (!currentPrompt) return;

                    const name = window.prompt(
                      'Enter a title for the new sub-prompt:',
                    );
                    if (!name) return;
                    const res = await fetch('/api/sub-prompts', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({
                        parentPromptId: currentPrompt.id,
                        name,
                        content: 'You are a specialized AI assistant.',
                      }),
                    });
                    if (res.ok) {
                      toast({
                        type: 'success',
                        description: 'Sub-prompt created successfully!',
                      });

                      // Revalidate sub-prompts using SWR
                      mutateCurrentSubPrompts();
                      // Select the first (newest) sub-prompt after revalidation
                      setSelectedSubPromptIndex(0);
                    } else {
                      toast({
                        type: 'error',
                        description: 'Failed to create sub-prompt.',
                      });
                    }
                  }}
                >
                  + New Sub-Prompt
                </button>
              </div>
            </div>
            {/* Right: Prompt Editor */}
            <div className="w-2/3 h-full flex flex-col">
              {/* Tab Navigation - only show for main prompts, not sub-prompts */}
              {selectedSubPromptIndex === null && (
                <div className="flex border-b">
                  <button
                    type="button"
                    className={`px-4 py-2 text-sm font-medium border-b-2 ${
                      activeTab === 'content'
                        ? 'border-primary text-primary'
                        : 'border-transparent text-muted-foreground hover:text-foreground'
                    }`}
                    onClick={() => setActiveTab('content')}
                  >
                    Content
                  </button>
                  <button
                    type="button"
                    className={`px-4 py-2 text-sm font-medium border-b-2 ${
                      activeTab === 'variables'
                        ? 'border-primary text-primary'
                        : 'border-transparent text-muted-foreground hover:text-foreground'
                    }`}
                    onClick={() => setActiveTab('variables')}
                  >
                    Variables
                  </button>
                </div>
              )}

              {/* Content Editor */}
              {(selectedSubPromptIndex !== null || activeTab === 'content') && (
                <textarea
                  className="flex-1 w-full p-4 text-base border-none outline-none resize-none bg-background"
                  value={editedContent}
                  onChange={(e) => setEditedContent(e.target.value)}
                  placeholder={
                    selectedSubPromptIndex !== null
                      ? 'Enter sub-prompt content here...'
                      : 'Enter prompt content here. Use {{variableName}} for placeholders.'
                  }
                />
              )}

              {/* Variables Editor */}
              {selectedSubPromptIndex === null && activeTab === 'variables' && (
                <div className="flex-1 p-4 overflow-y-auto">
                  <div className="space-y-4">
                    <div className="text-sm text-muted-foreground">
                      Define variables that can be used in your prompt content
                      using the format {'{{'} variableName {'}}'}.
                    </div>

                    {Object.entries(editedVariables).map(
                      ([key, value], _index) => (
                        <div key={`variable-${key}`} className="space-y-2">
                          <div className="flex items-center gap-2">
                            <input
                              type="text"
                              value={key}
                              onChange={(e) => {
                                const newKey = e.target.value;
                                const entries = Object.entries(editedVariables);
                                const newVariables: Record<string, string> = {};

                                entries.forEach(([k, v], i) => {
                                  if (i === index) {
                                    if (newKey.trim()) {
                                      newVariables[newKey] = v;
                                    }
                                  } else {
                                    newVariables[k] = v;
                                  }
                                });

                                setEditedVariables(newVariables);
                              }}
                              className="px-3 py-2 border rounded-md text-sm font-mono bg-background"
                              placeholder="Variable name"
                            />
                            <button
                              type="button"
                              onClick={() => {
                                const entries = Object.entries(editedVariables);
                                const newVariables: Record<string, string> = {};

                                entries.forEach(([k, v], i) => {
                                  if (i !== index) {
                                    newVariables[k] = v;
                                  }
                                });

                                setEditedVariables(newVariables);
                              }}
                              className="px-2 py-1 text-red-600 hover:bg-red-50 rounded text-sm"
                            >
                              Remove
                            </button>
                          </div>
                          <textarea
                            value={value}
                            onChange={(e) => {
                              const entries = Object.entries(editedVariables);
                              const newVariables: Record<string, string> = {};

                              entries.forEach(([k, v], i) => {
                                if (i === index) {
                                  newVariables[k] = e.target.value;
                                } else {
                                  newVariables[k] = v;
                                }
                              });

                              setEditedVariables(newVariables);
                            }}
                            className="w-full px-3 py-2 border rounded-md text-sm bg-background resize-none"
                            rows={3}
                            placeholder="Variable value"
                          />
                        </div>
                      ),
                    )}

                    <button
                      type="button"
                      onClick={() => {
                        const newKey = `variable${Object.keys(editedVariables).length + 1}`;
                        setEditedVariables({
                          ...editedVariables,
                          [newKey]: '',
                        });
                      }}
                      className="px-4 py-2 text-sm text-primary hover:bg-primary/10 rounded border border-primary"
                    >
                      + Add Variable
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
          <DialogFooter className="px-4 pb-4 pt-0 flex-row gap-2 !justify-between">
            <div className="flex justify-start">
              {((selectedSubPromptIndex !== null &&
                currentSubPrompts.length > 0) ||
                (selectedSubPromptIndex === null && prompts.length > 0)) && (
                <button
                  className="flex-1 py-2 text-red-600 font-medium hover:bg-muted rounded"
                  type="button"
                  onClick={async () => {
                    if (selectedSubPromptIndex !== null) {
                      // Delete sub-prompt
                      const subPrompt =
                        currentSubPrompts[selectedSubPromptIndex];
                      if (!subPrompt) return;
                      if (
                        !window.confirm(
                          `Delete sub-prompt '${subPrompt.name}'? This cannot be undone.`,
                        )
                      )
                        return;
                      const res = await fetch('/api/sub-prompts', {
                        method: 'DELETE',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ id: subPrompt.id }),
                      });
                      if (res.ok) {
                        toast({
                          type: 'success',
                          description: 'Sub-prompt deleted successfully!',
                        });
                        // Revalidate sub-prompts using SWR
                        mutateCurrentSubPrompts();
                        setSelectedSubPromptIndex(null);
                      } else {
                        toast({
                          type: 'error',
                          description: 'Failed to delete sub-prompt.',
                        });
                      }
                    } else {
                      // Delete main prompt
                      const prompt = prompts[selectedPromptIndex];
                      if (!prompt) return;
                      if (
                        !window.confirm(
                          `Delete prompt '${prompt.name}'? This cannot be undone.`,
                        )
                      )
                        return;
                      const res = await fetch('/api/prompts', {
                        method: 'DELETE',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ id: prompt.id }),
                      });
                      if (res.ok) {
                        toast({
                          type: 'success',
                          description: 'Prompt deleted successfully!',
                        });
                        await reload();
                        setSelectedPromptIndex(0);
                        setSelectedSubPromptIndex(null);
                      } else {
                        toast({
                          type: 'error',
                          description: 'Failed to delete prompt.',
                        });
                      }
                    }
                  }}
                >
                  Delete{' '}
                  {selectedSubPromptIndex !== null ? 'Sub-Prompt' : 'Prompt'}
                </button>
              )}
            </div>
            <div className="flex flex-row gap-2 justify-end">
              <button
                type="button"
                className="px-4 py-2 rounded-md border bg-background hover:bg-muted text-foreground"
                onClick={() => setIsManageOpen(false)}
              >
                Cancel
              </button>
              <button
                type="button"
                className="px-4 py-2 rounded-md bg-primary text-primary-foreground hover:bg-primary/90 font-medium"
                onClick={handleUpdate}
              >
                Update
              </button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DropdownMenu>
  );
}
