'use client';

import type { Attachment, UIMessage, TextPart } from 'ai';
import { useChat } from '@ai-sdk/react';
import { useEffect, useRef, useState, useCallback } from 'react';
import useSWR, { useSWRConfig } from 'swr';
import { ChatHeader } from '@/components/chat-header';
import type { Prompt, Vote } from '@/lib/db/schema';
import { fetcher, generateUUID } from '@/lib/utils';
import { Artifact } from './artifact';
import { MultimodalInput } from './multimodal-input';
import { Messages } from './messages';
import type { VisibilityType } from './visibility-selector';
import { useArtifactSelector } from '@/hooks/use-artifact';
import { unstable_serialize } from 'swr/infinite';
import { getChatHistoryPaginationKey } from './sidebar-history';
import { toast } from './toast';
import type { Session } from 'next-auth';
import { useSearchParams } from 'next/navigation';
import { useChatVisibility } from '@/hooks/use-chat-visibility';
import { useDefaultSelections } from '@/hooks/use-default-selections';
import { useAppState } from '@/hooks/use-app-state';
import { DEFAULT_CHAT_MODEL } from '@/lib/ai/models';
import { useCommentsState } from '@/hooks/use-comments';
import { CommentsSidebar } from './comments-sidebar';
import { useTTS } from '@/hooks/use-tts';
import { FirstMessageModal } from './first-message-modal';

export function Chat({
  id,
  initialMessages,
  initialChatModel,
  initialVisibilityType,
  isReadonly,
  session,
  autoResume,
  initialPromptId,
  hasSeenFirstMessageModal = false,
}: {
  id: string;
  initialMessages: Array<UIMessage>;
  initialChatModel: string;
  initialVisibilityType: VisibilityType;
  isReadonly: boolean;
  session: Session;
  autoResume: boolean;
  initialPromptId?: string;
  hasSeenFirstMessageModal?: boolean;
}) {
  const { mutate } = useSWRConfig();
  // Get default selections based on user type
  const { defaultModelId, defaultPrompt, isAdmin, isLoading } =
    useDefaultSelections();

  // TTS functionality
  const { isAutoTTSEnabled, speak } = useTTS();

  // Use app state for selected model and prompt
  const {
    selectedChatModel,
    setSelectedChatModel,
    selectedPrompt,
    setSelectedPrompt,
  } = useAppState();

  // Initialize selected model from cookie or default based on user type
  useEffect(() => {
    // Always prioritize initialChatModel (from cookie) for admins on page load
    if (isAdmin && initialChatModel) {
      setSelectedChatModel(initialChatModel);
    }
    // For non-admins or when no model is selected, use the defaultModelId
    else if (!selectedChatModel) {
      setSelectedChatModel(isAdmin ? initialChatModel : defaultModelId || '');
    }
  }, [
    isAdmin,
    initialChatModel,
    defaultModelId,
    selectedChatModel,
    setSelectedChatModel,
  ]);

  // Create refs for the AI SDK to use
  const selectedChatModelRef = useRef<string | null>(selectedChatModel);
  const selectedPromptRef = useRef<Prompt | null>(selectedPrompt);

  const { visibilityType } = useChatVisibility({
    chatId: id,
    initialVisibilityType,
  });

  useEffect(() => {
    selectedPromptRef.current = selectedPrompt;
  }, [selectedPrompt]);

  useEffect(() => {
    selectedChatModelRef.current = selectedChatModel;
  }, [selectedChatModel]);

  const {
    messages,
    setMessages,
    handleSubmit,
    input,
    setInput,
    append,
    status,
    stop,
    reload,
    experimental_resume,
  } = useChat({
    id,
    initialMessages,
    experimental_throttle: 100,
    sendExtraMessageFields: true,
    generateId: generateUUID,
    experimental_prepareRequestBody: (body) => {
      const lastMessage = body.messages.at(-1);

      const requestBody = {
        id,
        message: lastMessage,
        selectedChatModel: selectedChatModelRef.current,
        selectedVisibilityType: visibilityType,
        promptId: selectedPromptRef.current?.id,
      };
      return requestBody;
    },

    onFinish: (message) => {
      mutate(unstable_serialize(getChatHistoryPaginationKey));

      if (isAutoTTSEnabled && message.role === 'assistant') {
        const textFromParts = message.parts
          ?.filter((part): part is TextPart => part.type === 'text')
          .map((part) => part.text)
          .join('\n')
          .trim();

        if (textFromParts) {
          speak(textFromParts, message.id);
        }
      }
    },
    onError: (error) => {
      toast({
        type: 'error',
        description: error.message,
      });
    },
  });

  // Set default prompt just once when it becomes available
  useEffect(() => {
    // Only set it once when defaultPrompt is available and selectedPrompt isn't
    if (defaultPrompt && !selectedPrompt && !isLoading) {
      setSelectedPrompt(defaultPrompt);
    }
  }, [defaultPrompt, selectedPrompt, isLoading, setSelectedPrompt]);

  // Commented out because this was triggering onFinish callback when loading existing conversations, triggerring the Auto TTS
  // useEffect(() => {
  //   if (autoResume) {
  //     experimental_resume();
  //   }

  //   // note: this hook has no dependencies since it only needs to run once
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, []);

  const searchParams = useSearchParams();
  const query = searchParams.get('query');

  const [hasAppendedQuery, setHasAppendedQuery] = useState(false);

  useEffect(() => {
    if (query && !hasAppendedQuery) {
      append({
        role: 'user',
        content: query,
      });

      setHasAppendedQuery(true);
      window.history.replaceState({}, '', `/chat/${id}`);
    }
  }, [query, append, hasAppendedQuery, id]);

  const { data: votes } = useSWR<Array<Vote>>(
    messages.length >= 2 ? `/api/vote?chatId=${id}` : null,
    fetcher,
  );

  const [attachments, setAttachments] = useState<Array<Attachment>>([]);
  const isArtifactVisible = useArtifactSelector((state) => state.isVisible);

  // First message modal state
  const [showFirstMessageModal, setShowFirstMessageModal] = useState(false);
  const [pendingFirstMessage, setPendingFirstMessage] = useState<{
    message: any;
    options?: any;
  } | null>(null);

  // Comments functionality
  const {
    selectedMessageId,
    isCommentsSidebarOpen,
    openCommentsForMessage,
    closeCommentsSidebar,
    selectedText,
  } = useCommentsState();

  const handlePromptSelect = (prompt: Prompt) => {
    setSelectedPrompt(prompt);
  };

  // Handle first message modal flow
  const handleFirstMessageSubmit = useCallback((message: any, options?: any) => {
    // Check if this is a new conversation (no existing messages) and user hasn't seen modal
    if (initialMessages.length === 0 && messages.length === 0 && !hasSeenFirstMessageModal) {
      // Store the pending message and show modal
      setPendingFirstMessage({ message, options });
      setShowFirstMessageModal(true);
    } else {
      // Normal message submission
      if (message) {
        append(message);
      } else {
        handleSubmit(message, options);
      }
    }
  }, [initialMessages.length, messages.length, handleSubmit, hasSeenFirstMessageModal, append]);

  const handleModalProceed = useCallback(async () => {
    setShowFirstMessageModal(false);
    
    // Set the client-side cookie to indicate the user has seen the modal
    document.cookie = `first-message-modal-shown=true; path=/; max-age=${60 * 60 * 24 * 365}; SameSite=strict${process.env.NODE_ENV === 'production' ? '; Secure' : ''}`;
    
    if (pendingFirstMessage) {
      // Send the pending message
      if (pendingFirstMessage.message) {
        append(pendingFirstMessage.message);
      }
      else {
        handleSubmit(
          pendingFirstMessage.message,
          pendingFirstMessage.options
        );
      }
      setPendingFirstMessage(null);
    }
  }, [pendingFirstMessage, handleSubmit, append]);

  return (
    <>
      <div className="flex flex-col min-w-0 h-dvh bg-background">
        <ChatHeader
          chatId={id}
          selectedModelId={selectedChatModel || DEFAULT_CHAT_MODEL}
          selectedVisibilityType={initialVisibilityType}
          isReadonly={isReadonly}
          session={session}
          onModelChange={setSelectedChatModel}
          onPromptSelect={handlePromptSelect}
          initialPromptId={initialPromptId}
        />

        <Messages
          chatId={id}
          status={status}
          votes={votes}
          messages={messages}
          setMessages={setMessages}
          reload={reload}
          isReadonly={isReadonly}
          isArtifactVisible={isArtifactVisible}
          onOpenComments={openCommentsForMessage}
          isOwner={!isReadonly}
          isAdmin={isAdmin}
        />

        <form className="flex mx-auto px-4 bg-background pb-4 md:pb-6 gap-2 w-full md:max-w-3xl">
          {!isReadonly && (
            <MultimodalInput
              chatId={id}
              input={input}
              setInput={setInput}
              handleSubmit={handleFirstMessageSubmit}
              status={status}
              stop={stop}
              attachments={attachments}
              setAttachments={setAttachments}
              messages={messages}
              setMessages={setMessages}
              append={append}
              selectedVisibilityType={visibilityType}
            />
          )}
        </form>
      </div>

      <Artifact
        chatId={id}
        input={input}
        setInput={setInput}
        handleSubmit={handleSubmit}
        status={status}
        stop={stop}
        attachments={attachments}
        setAttachments={setAttachments}
        append={append}
        messages={messages}
        setMessages={setMessages}
        reload={reload}
        votes={votes}
        isReadonly={isReadonly}
        selectedVisibilityType={visibilityType}
      />

      <CommentsSidebar
        messageId={selectedMessageId}
        isOpen={isCommentsSidebarOpen}
        onClose={closeCommentsSidebar}
        session={session}
        selectedText={selectedText}
      />

      <FirstMessageModal
        isOpen={showFirstMessageModal}
        onProceed={handleModalProceed}
      />
    </>
  );
}
