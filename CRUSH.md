# CRUSH.md - Development Guidelines

## Build/Lint/Test Commands
- `pnpm dev` - Start development server
- `pnpm build` - Build for production (runs migrations first)
- `pnpm lint` - Lint code with ESLint and Biome
- `pnpm lint:fix` - Fix linting issues
- `pnpm format` - Format code with Biome
- `pnpm test` - Run all Playwright tests
- `npx playwright test <file>` - Run specific test file

## Database Commands
- `pnpm db:migrate` - Run database migrations
- `pnpm db:generate` - Generate migrations from schema changes
- `pnpm db:studio` - View database with Drizzle Studio

## Code Style Guidelines
- Use single quotes for strings, double quotes for JSX attributes
- biome.jsonc enforces formatting (2-space indent, 80 line width, LF line endings)
- Prefer arrow functions and functional components
- Use TypeScript for all new code
- Use clsx/tailwind-merge for conditional classNames
- Server-only database operations in lib/db/queries.ts
- Client components should use API routes or hooks for data fetching

## Naming Conventions
- Component files: PascalCase.tsx
- Utility files: camelCase.ts
- Test files: *.test.ts
- Hook files: use-*.ts

## Import Order
1. React/Next.js core imports
2. Third-party libraries
3. Local imports (lib/, components/, hooks/)
4. Relative imports (./, ../)

## Error Handling
- Use try/catch blocks for async operations
- Display user-friendly error messages with toast notifications
- Log errors appropriately for debugging

## Testing
- Playwright for E2E tests
- Unit tests for critical logic
- Test files colocated with implementation

## AI Coding Guidelines
- Do NOT add tests to the codebase
- Do NOT run the development server
- Solutions should be optimal and change minimal code
- Focus on the "why" rather than the "what"